package com.teyuntong.goods.search.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.search.TestBase;
import com.teyuntong.goods.search.service.biz.push.dto.PushGoodsConfigAddressDTO;
import com.teyuntong.goods.search.service.biz.push.dto.PushGoodsConfigSaveDTO;
import com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigAddressDO;
import com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigDO;
import com.teyuntong.goods.search.service.biz.push.mapper.PushGoodsConfigAddressMapper;
import com.teyuntong.goods.search.service.biz.push.mapper.PushGoodsConfigMapper;
import com.teyuntong.goods.search.service.biz.push.service.PushGoodsConfigService;
import com.teyuntong.goods.search.service.biz.push.vo.PushGoodsConfigAddressVO;
import com.teyuntong.goods.search.service.biz.push.vo.PushGoodsConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 推送货源配置服务测试类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PushGoodsConfigServiceTest extends TestBase {

    @Autowired
    private PushGoodsConfigService pushGoodsConfigService;

    @Autowired
    private PushGoodsConfigMapper pushGoodsConfigMapper;

    @Autowired
    private PushGoodsConfigAddressMapper pushGoodsConfigAddressMapper;

    private static final Long TEST_USER_ID = 999999999L; // 测试用户ID

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cleanTestData();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        cleanTestData();
    }

    /**
     * 测试保存推送货源配置 - 新增场景
     */
    @Test
    @Order(1)
    void testSavePushGoodsConfig_Insert() {
        log.info("开始测试保存推送货源配置 - 新增场景");

        // 准备测试数据
        PushGoodsConfigSaveDTO saveDTO = createTestSaveDTO();

        // 执行保存
        pushGoodsConfigService.savePushGoodsConfig(TEST_USER_ID, saveDTO);

        // 验证主配置是否保存成功
        PushGoodsConfigDO mainConfig = pushGoodsConfigMapper.selectByUserId(TEST_USER_ID);
        Assertions.assertNotNull(mainConfig, "主配置应该保存成功");
        Assertions.assertEquals(saveDTO.getVehicleTypeName(), mainConfig.getVehicleTypeName());
        Assertions.assertEquals(saveDTO.getVehicleTypeCode(), mainConfig.getVehicleTypeCode());
        Assertions.assertEquals(saveDTO.getWeightHigh(), mainConfig.getWeightHigh());
        Assertions.assertEquals(saveDTO.getWeightLow(), mainConfig.getWeightLow());
        Assertions.assertEquals(saveDTO.getPushSwitch(), mainConfig.getPushSwitch());
        Assertions.assertEquals(saveDTO.getDailyStartTime(), mainConfig.getDailyStartTime());
        Assertions.assertEquals(saveDTO.getDailyEndTime(), mainConfig.getDailyEndTime());

        // 验证地址配置是否保存成功
        List<PushGoodsConfigAddressDO> addressList = pushGoodsConfigAddressMapper.selectByUserId(TEST_USER_ID);
        Assertions.assertNotNull(addressList, "地址配置应该保存成功");
        
        // 验证出发地配置
        List<PushGoodsConfigAddressDO> startAddressList = addressList.stream()
                .filter(addr -> addr.getType() == 1)
                .toList();
        Assertions.assertEquals(4, startAddressList.size(), "出发地配置数量应该为4");

        // 验证目的地配置
        List<PushGoodsConfigAddressDO> destAddressList = addressList.stream()
                .filter(addr -> addr.getType() == 2)
                .toList();
        Assertions.assertEquals(2, destAddressList.size(), "目的地配置数量应该为2");

        log.info("保存推送货源配置 - 新增场景测试完成");
    }

    /**
     * 测试保存推送货源配置 - 更新场景
     */
    @Test
    @Order(2)
    void testSavePushGoodsConfig_Update() {
        log.info("开始测试保存推送货源配置 - 更新场景");

        // 先保存一次
        PushGoodsConfigSaveDTO firstSaveDTO = createTestSaveDTO();
        pushGoodsConfigService.savePushGoodsConfig(TEST_USER_ID, firstSaveDTO);

        // 准备更新数据
        PushGoodsConfigSaveDTO updateDTO = createTestUpdateDTO();

        // 执行更新
        pushGoodsConfigService.savePushGoodsConfig(TEST_USER_ID, updateDTO);

        // 验证主配置是否更新成功
        PushGoodsConfigDO mainConfig = pushGoodsConfigMapper.selectByUserId(TEST_USER_ID);
        Assertions.assertNotNull(mainConfig, "主配置应该存在");
        Assertions.assertEquals(updateDTO.getVehicleTypeName(), mainConfig.getVehicleTypeName());
        Assertions.assertEquals(updateDTO.getVehicleTypeCode(), mainConfig.getVehicleTypeCode());
        Assertions.assertEquals(updateDTO.getWeightHigh(), mainConfig.getWeightHigh());
        Assertions.assertEquals(updateDTO.getWeightLow(), mainConfig.getWeightLow());
        Assertions.assertEquals(updateDTO.getPushSwitch(), mainConfig.getPushSwitch());

        // 验证地址配置是否更新成功（应该是覆盖更新）
        List<PushGoodsConfigAddressDO> addressList = pushGoodsConfigAddressMapper.selectByUserId(TEST_USER_ID);
        Assertions.assertNotNull(addressList, "地址配置应该存在");
        
        // 验证出发地配置数量变化
        List<PushGoodsConfigAddressDO> startAddressList = addressList.stream()
                .filter(addr -> addr.getType() == 1)
                .toList();
        Assertions.assertEquals(1, startAddressList.size(), "更新后出发地配置数量应该为1");

        // 验证目的地配置数量变化
        List<PushGoodsConfigAddressDO> destAddressList = addressList.stream()
                .filter(addr -> addr.getType() == 2)
                .toList();
        Assertions.assertEquals(3, destAddressList.size(), "更新后目的地配置数量应该为3");

        log.info("保存推送货源配置 - 更新场景测试完成");
    }

    /**
     * 测试查询推送货源配置详情 - 有数据场景
     */
    @Test
    @Order(3)
    void testGetPushGoodsConfig_WithData() {
        log.info("开始测试查询推送货源配置详情 - 有数据场景");

        // 先保存数据
        PushGoodsConfigSaveDTO saveDTO = createTestSaveDTO();
        pushGoodsConfigService.savePushGoodsConfig(TEST_USER_ID, saveDTO);

        // 执行查询
        PushGoodsConfigVO result = pushGoodsConfigService.getPushGoodsConfig(TEST_USER_ID);

        // 验证查询结果
        Assertions.assertNotNull(result, "查询结果不应该为空");
        Assertions.assertEquals(saveDTO.getVehicleTypeName(), result.getVehicleTypeName());
        Assertions.assertEquals(saveDTO.getVehicleTypeCode(), result.getVehicleTypeCode());
        Assertions.assertEquals(saveDTO.getWeightHigh(), result.getWeightHigh());
        Assertions.assertEquals(saveDTO.getWeightLow(), result.getWeightLow());
        Assertions.assertEquals(saveDTO.getPushSwitch(), result.getPushSwitch());
        Assertions.assertEquals(saveDTO.getDailyStartTime(), result.getDailyStartTime());
        Assertions.assertEquals(saveDTO.getDailyEndTime(), result.getDailyEndTime());

        // 验证出发地配置
        Assertions.assertNotNull(result.getStartAddressList(), "出发地配置列表不应该为空");
        Assertions.assertEquals(4, result.getStartAddressList().size(), "出发地配置数量应该为4");

        // 验证目的地配置
        Assertions.assertNotNull(result.getDestAddressList(), "目的地配置列表不应该为空");
        Assertions.assertEquals(2, result.getDestAddressList().size(), "目的地配置数量应该为2");

        // 验证具体地址数据
        PushGoodsConfigAddressVO firstStartAddress = result.getStartAddressList().get(0);
        Assertions.assertEquals("北京", firstStartAddress.getProvince());
        Assertions.assertEquals("北京市", firstStartAddress.getCity());
        Assertions.assertEquals("海淀区", firstStartAddress.getDistrict());

        log.info("查询推送货源配置详情 - 有数据场景测试完成，结果: {}", JSONUtil.toJsonStr(result));
    }

    /**
     * 测试查询推送货源配置详情 - 无数据场景
     */
    @Test
    @Order(4)
    void testGetPushGoodsConfig_NoData() {
        log.info("开始测试查询推送货源配置详情 - 无数据场景");

        // 执行查询（不保存任何数据）
        PushGoodsConfigVO result = pushGoodsConfigService.getPushGoodsConfig(TEST_USER_ID);

        // 验证查询结果
        Assertions.assertNull(result, "查询结果应该为空");

        log.info("查询推送货源配置详情 - 无数据场景测试完成");
    }

    /**
     * 测试保存空地址列表
     */
    @Test
    @Order(5)
    void testSavePushGoodsConfig_EmptyAddressList() {
        log.info("开始测试保存空地址列表");

        // 准备测试数据（空地址列表）
        PushGoodsConfigSaveDTO saveDTO = new PushGoodsConfigSaveDTO();
        saveDTO.setVehicleTypeName("测试车型");
        saveDTO.setVehicleTypeCode("TEST_TYPE");
        saveDTO.setWeightHigh(new BigDecimal("20.0"));
        saveDTO.setWeightLow(new BigDecimal("1.0"));
        saveDTO.setPushSwitch(1);
        saveDTO.setDailyStartTime(8);
        saveDTO.setDailyEndTime(20);
        saveDTO.setPushDeadline(new Date());
        saveDTO.setStartAddressList(new ArrayList<>()); // 空列表
        saveDTO.setDestAddressList(new ArrayList<>()); // 空列表

        // 执行保存
        pushGoodsConfigService.savePushGoodsConfig(TEST_USER_ID, saveDTO);

        // 验证主配置保存成功
        PushGoodsConfigDO mainConfig = pushGoodsConfigMapper.selectByUserId(TEST_USER_ID);
        Assertions.assertNotNull(mainConfig, "主配置应该保存成功");

        // 验证地址配置为空
        List<PushGoodsConfigAddressDO> addressList = pushGoodsConfigAddressMapper.selectByUserId(TEST_USER_ID);
        Assertions.assertTrue(CollUtil.isEmpty(addressList), "地址配置应该为空");

        log.info("保存空地址列表测试完成");
    }

    /**
     * 创建测试保存DTO
     */
    private PushGoodsConfigSaveDTO createTestSaveDTO() {
        PushGoodsConfigSaveDTO saveDTO = new PushGoodsConfigSaveDTO();
        saveDTO.setVehicleTypeName("厢式货车");
        saveDTO.setVehicleTypeCode("BOX_TRUCK");
        saveDTO.setWeightHigh(new BigDecimal("10.50"));
        saveDTO.setWeightLow(new BigDecimal("5.00"));
        saveDTO.setPushSwitch(1);
        saveDTO.setDailyStartTime(9);
        saveDTO.setDailyEndTime(18);
        saveDTO.setPushDeadline(new Date());

        // 出发地配置
        List<PushGoodsConfigAddressDTO> startAddressList = new ArrayList<>();
        startAddressList.add(createAddressDTO("北京", "北京市", "海淀区"));
        startAddressList.add(createAddressDTO("北京", "北京市", "朝阳区"));
        startAddressList.add(createAddressDTO("河南省", "", ""));
        startAddressList.add(createAddressDTO("河南省", "洛阳市", ""));
        saveDTO.setStartAddressList(startAddressList);

        // 目的地配置
        List<PushGoodsConfigAddressDTO> destAddressList = new ArrayList<>();
        destAddressList.add(createAddressDTO("上海", "上海市", "浦东新区"));
        destAddressList.add(createAddressDTO("广东省", "深圳市", "南山区"));
        saveDTO.setDestAddressList(destAddressList);

        return saveDTO;
    }

    /**
     * 创建测试更新DTO
     */
    private PushGoodsConfigSaveDTO createTestUpdateDTO() {
        PushGoodsConfigSaveDTO updateDTO = new PushGoodsConfigSaveDTO();
        updateDTO.setVehicleTypeName("平板货车");
        updateDTO.setVehicleTypeCode("FLAT_TRUCK");
        updateDTO.setWeightHigh(new BigDecimal("15.00"));
        updateDTO.setWeightLow(new BigDecimal("3.00"));
        updateDTO.setPushSwitch(0);
        updateDTO.setDailyStartTime(8);
        updateDTO.setDailyEndTime(20);
        updateDTO.setPushDeadline(new Date());

        // 更新后的出发地配置（数量减少）
        List<PushGoodsConfigAddressDTO> startAddressList = new ArrayList<>();
        startAddressList.add(createAddressDTO("江苏省", "南京市", ""));
        updateDTO.setStartAddressList(startAddressList);

        // 更新后的目的地配置（数量增加）
        List<PushGoodsConfigAddressDTO> destAddressList = new ArrayList<>();
        destAddressList.add(createAddressDTO("浙江省", "杭州市", "西湖区"));
        destAddressList.add(createAddressDTO("山东省", "青岛市", ""));
        destAddressList.add(createAddressDTO("四川省", "", ""));
        updateDTO.setDestAddressList(destAddressList);

        return updateDTO;
    }

    /**
     * 创建地址DTO
     */
    private PushGoodsConfigAddressDTO createAddressDTO(String province, String city, String district) {
        PushGoodsConfigAddressDTO addressDTO = new PushGoodsConfigAddressDTO();
        addressDTO.setProvince(province);
        addressDTO.setCity(city);
        addressDTO.setDistrict(district);
        return addressDTO;
    }

    /**
     * 清理测试数据
     */
    private void cleanTestData() {
        try {
            // 删除地址配置
            pushGoodsConfigAddressMapper.deleteByUserId(TEST_USER_ID);
            
            // 删除主配置
            PushGoodsConfigDO existConfig = pushGoodsConfigMapper.selectByUserId(TEST_USER_ID);
            if (existConfig != null) {
                pushGoodsConfigMapper.deleteById(existConfig.getId());
            }
            
            log.info("测试数据清理完成");
        } catch (Exception e) {
            log.warn("清理测试数据时发生异常: {}", e.getMessage());
        }
    }
}
