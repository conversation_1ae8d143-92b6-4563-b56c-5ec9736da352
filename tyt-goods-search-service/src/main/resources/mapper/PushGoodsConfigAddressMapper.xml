<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.push.mapper.PushGoodsConfigAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigAddressDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="type" property="type" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="district" property="district" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, type, province, city, district, create_time, modify_time
    </sql>

    <!-- 根据用户ID查询地址配置列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tyt_wechat_push_goods_config_address
        WHERE user_id = #{userId}
        ORDER BY type, id
    </select>

    <!-- 根据用户ID和类型查询地址配置列表 -->
    <select id="selectByUserIdAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tyt_wechat_push_goods_config_address
        WHERE user_id = #{userId} AND type = #{type}
        ORDER BY id
    </select>

    <!-- 根据用户ID删除地址配置 -->
    <delete id="deleteByUserId">
        DELETE FROM tyt_wechat_push_goods_config_address
        WHERE user_id = #{userId}
    </delete>

</mapper>
