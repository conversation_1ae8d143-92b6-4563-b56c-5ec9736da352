<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.push.mapper.PushGoodsConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="vehicle_type_name" property="vehicleTypeName" />
        <result column="vehicle_type_code" property="vehicleTypeCode" />
        <result column="weight_high" property="weightHigh" />
        <result column="weight_low" property="weightLow" />
        <result column="push_switch" property="pushSwitch" />
        <result column="daily_start_time" property="dailyStartTime" />
        <result column="daily_end_time" property="dailyEndTime" />
        <result column="push_deadline" property="pushDeadline" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, vehicle_type_name, vehicle_type_code, weight_high, weight_low, 
        push_switch, daily_start_time, daily_end_time, push_deadline, create_time, modify_time
    </sql>

    <!-- 根据用户ID查询配置 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tyt_wechat_push_goods_config
        WHERE user_id = #{userId}
    </select>

</mapper>
