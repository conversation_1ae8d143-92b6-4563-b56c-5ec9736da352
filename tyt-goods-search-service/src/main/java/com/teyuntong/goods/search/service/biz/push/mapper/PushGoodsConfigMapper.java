package com.teyuntong.goods.search.service.biz.push.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 推送货源配置主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Mapper
@DS("tyt")
public interface PushGoodsConfigMapper extends BaseMapper<PushGoodsConfigDO> {

    /**
     * 根据用户ID查询配置
     *
     * @param userId 用户ID
     * @return 配置信息
     */
    PushGoodsConfigDO selectByUserId(@Param("userId") Long userId);
}
