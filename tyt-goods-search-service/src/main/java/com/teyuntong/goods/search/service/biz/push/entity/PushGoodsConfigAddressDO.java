package com.teyuntong.goods.search.service.biz.push.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 推送货源配置地址表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@Setter
@TableName("tyt_wechat_push_goods_config_address")
public class PushGoodsConfigAddressDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 地址类型 1:出发地 2:目的地
     */
    private Integer type;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
