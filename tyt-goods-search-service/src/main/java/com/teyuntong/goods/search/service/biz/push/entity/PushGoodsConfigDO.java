package com.teyuntong.goods.search.service.biz.push.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 推送货源配置主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@Setter
@TableName("tyt_wechat_push_goods_config")
public class PushGoodsConfigDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 车辆类型名称
     */
    private String vehicleTypeName;

    /**
     * 车辆类型code
     */
    private String vehicleTypeCode;

    /**
     * 货物重量高值
     */
    private BigDecimal weightHigh;

    /**
     * 货物重量低值
     */
    private BigDecimal weightLow;

    /**
     * 推送开关 0:关闭 1:开启
     */
    private Integer pushSwitch;

    /**
     * 每日推送开始时间(24小时制，存数字)
     */
    private Integer dailyStartTime;

    /**
     * 每日推送结束时间(24小时制，存数字)
     */
    private Integer dailyEndTime;

    /**
     * 推送截止日期
     */
    private Date pushDeadline;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
