package com.teyuntong.goods.search.service.biz.push.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigAddressDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 推送货源配置地址表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Mapper
@DS("tyt")
public interface PushGoodsConfigAddressMapper extends BaseMapper<PushGoodsConfigAddressDO> {

    /**
     * 根据用户ID查询地址配置列表
     *
     * @param userId 用户ID
     * @return 地址配置列表
     */
    List<PushGoodsConfigAddressDO> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和类型查询地址配置列表
     *
     * @param userId 用户ID
     * @param type   地址类型 1:出发地 2:目的地
     * @return 地址配置列表
     */
    List<PushGoodsConfigAddressDO> selectByUserIdAndType(@Param("userId") Long userId, @Param("type") Integer type);

    /**
     * 根据用户ID删除地址配置
     *
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(@Param("userId") Long userId);
}
