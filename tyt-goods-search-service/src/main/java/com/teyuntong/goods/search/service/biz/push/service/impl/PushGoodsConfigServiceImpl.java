package com.teyuntong.goods.search.service.biz.push.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.search.service.biz.push.dto.PushGoodsConfigAddressDTO;
import com.teyuntong.goods.search.service.biz.push.dto.PushGoodsConfigSaveDTO;
import com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigAddressDO;
import com.teyuntong.goods.search.service.biz.push.entity.PushGoodsConfigDO;
import com.teyuntong.goods.search.service.biz.push.mapper.PushGoodsConfigAddressMapper;
import com.teyuntong.goods.search.service.biz.push.mapper.PushGoodsConfigMapper;
import com.teyuntong.goods.search.service.biz.push.service.PushGoodsConfigService;
import com.teyuntong.goods.search.service.biz.push.vo.PushGoodsConfigAddressVO;
import com.teyuntong.goods.search.service.biz.push.vo.PushGoodsConfigVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 推送货源配置服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PushGoodsConfigServiceImpl implements PushGoodsConfigService {

    private final PushGoodsConfigMapper pushGoodsConfigMapper;
    private final PushGoodsConfigAddressMapper pushGoodsConfigAddressMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePushGoodsConfig(Long userId, PushGoodsConfigSaveDTO saveDTO) {
        // 保存主配置
        saveMainConfig(userId, saveDTO);
        
        // 删除原有地址配置
        pushGoodsConfigAddressMapper.deleteByUserId(userId);
        
        // 保存出发地配置
        saveAddressConfig(userId, saveDTO.getStartAddressList(), 1);
        
        // 保存目的地配置
        saveAddressConfig(userId, saveDTO.getDestAddressList(), 2);
    }

    @Override
    public PushGoodsConfigVO getPushGoodsConfig(Long userId) {
        // 查询主配置
        PushGoodsConfigDO mainConfig = pushGoodsConfigMapper.selectByUserId(userId);
        if (mainConfig == null) {
            return null;
        }
        
        PushGoodsConfigVO vo = new PushGoodsConfigVO();
        BeanUtils.copyProperties(mainConfig, vo);
        
        // 查询地址配置
        List<PushGoodsConfigAddressDO> addressList = pushGoodsConfigAddressMapper.selectByUserId(userId);
        
        // 分离出发地和目的地
        List<PushGoodsConfigAddressVO> startAddressList = new ArrayList<>();
        List<PushGoodsConfigAddressVO> destAddressList = new ArrayList<>();
        
        if (CollUtil.isNotEmpty(addressList)) {
            for (PushGoodsConfigAddressDO addressDO : addressList) {
                PushGoodsConfigAddressVO addressVO = new PushGoodsConfigAddressVO();
                BeanUtils.copyProperties(addressDO, addressVO);
                
                if (addressDO.getType() == 1) {
                    startAddressList.add(addressVO);
                } else if (addressDO.getType() == 2) {
                    destAddressList.add(addressVO);
                }
            }
        }
        
        vo.setStartAddressList(startAddressList);
        vo.setDestAddressList(destAddressList);
        
        return vo;
    }

    /**
     * 保存主配置
     */
    private void saveMainConfig(Long userId, PushGoodsConfigSaveDTO saveDTO) {
        if (saveDTO != null && saveDTO.getPushDeadline() != null) {
            //设置这个字段时分秒为 23：59：59
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(saveDTO.getPushDeadline());
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0，防止进位
            saveDTO.setPushDeadline(calendar.getTime());
        }

        PushGoodsConfigDO existConfig = pushGoodsConfigMapper.selectByUserId(userId);
        
        if (existConfig != null) {
            // 更新现有配置
            BeanUtils.copyProperties(saveDTO, existConfig);
            existConfig.setModifyTime(new Date());
            pushGoodsConfigMapper.updateById(existConfig);
        } else {
            // 新增配置
            PushGoodsConfigDO newConfig = new PushGoodsConfigDO();
            BeanUtils.copyProperties(saveDTO, newConfig);
            newConfig.setUserId(userId);
            newConfig.setCreateTime(new Date());
            newConfig.setModifyTime(new Date());
            pushGoodsConfigMapper.insert(newConfig);
        }
    }

    /**
     * 保存地址配置
     */
    private void saveAddressConfig(Long userId, List<PushGoodsConfigAddressDTO> addressList, Integer type) {
        if (CollUtil.isEmpty(addressList)) {
            return;
        }
        
        Date now = new Date();
        List<PushGoodsConfigAddressDO> addressDOList = addressList.stream().map(dto -> {
            PushGoodsConfigAddressDO addressDO = new PushGoodsConfigAddressDO();
            BeanUtils.copyProperties(dto, addressDO);
            addressDO.setUserId(userId);
            addressDO.setType(type);
            addressDO.setCreateTime(now);
            addressDO.setModifyTime(now);
            return addressDO;
        }).toList();
        
        // 批量插入
        for (PushGoodsConfigAddressDO addressDO : addressDOList) {
            pushGoodsConfigAddressMapper.insert(addressDO);
        }
    }
}
