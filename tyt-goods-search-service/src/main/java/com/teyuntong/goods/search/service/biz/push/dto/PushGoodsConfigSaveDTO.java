package com.teyuntong.goods.search.service.biz.push.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 推送货源配置保存DTO
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@Slf4j
public class PushGoodsConfigSaveDTO {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 车辆类型名称
     */
    private String vehicleTypeName;

    /**
     * 车辆类型code
     */
    private String vehicleTypeCode;

    /**
     * 货物重量高值
     */
    private BigDecimal weightHigh;

    /**
     * 货物重量低值
     */
    private BigDecimal weightLow;

    /**
     * 推送开关 0:关闭 1:开启
     */
    private Integer pushSwitch;

    /**
     * 每日推送开始时间(24小时制，存数字)
     */
    private Integer dailyStartTime;

    /**
     * 每日推送结束时间(24小时制，存数字)
     */
    private Integer dailyEndTime;

    /**
     * 推送截止日期
     */
    private Date pushDeadline;

    /**
     * 出发地配置列表
     */
    private String startAddressListJsonString;

    /**
     * 目的地配置列表
     */
    private String destAddressListJsonString;

    /**
     * 出发地配置列表
     */
    private List<PushGoodsConfigAddressDTO> startAddressList;

    /**
     * 目的地配置列表
     */
    private List<PushGoodsConfigAddressDTO> destAddressList;
}
