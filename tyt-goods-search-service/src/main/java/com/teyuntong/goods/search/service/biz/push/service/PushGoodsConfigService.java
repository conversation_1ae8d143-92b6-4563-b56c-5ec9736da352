package com.teyuntong.goods.search.service.biz.push.service;

import com.teyuntong.goods.search.service.biz.push.dto.PushGoodsConfigSaveDTO;
import com.teyuntong.goods.search.service.biz.push.vo.PushGoodsConfigVO;

/**
 * 推送货源配置服务接口
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public interface PushGoodsConfigService {

    /**
     * 保存推送货源配置
     *
     * @param userId  用户ID
     * @param saveDTO 保存DTO
     */
    void savePushGoodsConfig(Long userId, PushGoodsConfigSaveDTO saveDTO);

    /**
     * 查询推送货源配置详情
     *
     * @param userId 用户ID
     * @return 配置详情
     */
    PushGoodsConfigVO getPushGoodsConfig(Long userId);
}
